<?php
$menus = [
    'admin'=>[
        'url'   => route('admin.index'),
        'title' => __("Dashboard"),
        'icon'  => 'icon ion-ios-desktop',
        "position"=>0
    ],
    'menu'=>[
        "position"=>60,
        'url'        => route('core.admin.menu.index'),
        'title'      => __("Menu"),
        'icon'       => 'icon ion-ios-apps',
        'permission' => 'menu_view',
    ],
    'general'=>[
        "position"=>80,
        'url'        => route('core.admin.settings.index',['group'=>'general']),
        'title'      => __('Setting'),
        'icon'       => 'icon ion-ios-cog',
        'permission' => 'setting_update',
        'children'   => \Modules\Core\Models\Settings::getSettingPages(true)
    ],
];

// Modules
$custom_modules = \Modules\ServiceProvider::getActivatedModules();
if(!empty($custom_modules)){
    $custom_modules[] = [
        'id'=>'theme',
        'class'=>\Modules\Theme\ModuleProvider::class
    ];
    foreach($custom_modules as $moduleData){
        $module = $moduleData['id'];
        $moduleClass = $moduleData['class'];
        if(class_exists($moduleClass))
        {
            $menuConfig = call_user_func([$moduleClass,'getAdminMenu']);

            if(!empty($menuConfig)){
                $menus = array_merge($menus,$menuConfig);
            }

            $menuSubMenu = call_user_func([$moduleClass,'getAdminSubMenu']);

            if(!empty($menuSubMenu)){
                foreach($menuSubMenu as $k=>$submenu){
                    $submenu['id'] = $submenu['id'] ?? '_'.$k;

                    if(!empty($submenu['parent']) and isset($menus[$submenu['parent']])){
                        $menus[$submenu['parent']]['children'][$submenu['id']] = $submenu;
                        $menus[$submenu['parent']]['children'] = array_values(\Illuminate\Support\Arr::sort($menus[$submenu['parent']]['children'], function ($value) {
                            return $value['position'] ?? 100;
                        }));
                    }
                }

            }
        }

    }
}

// Plugins Menu
$plugins_modules = \Plugins\ServiceProvider::getModules();
if(!empty($plugins_modules)){
    foreach($plugins_modules as $module){
        $moduleClass = "\\Plugins\\".ucfirst($module)."\\ModuleProvider";
        if(class_exists($moduleClass))
        {
            $menuConfig = call_user_func([$moduleClass,'getAdminMenu']);
            if(!empty($menuConfig)){
                $menus = array_merge($menus,$menuConfig);
            }
            $menuSubMenu = call_user_func([$moduleClass,'getAdminSubMenu']);
            if(!empty($menuSubMenu)){
                foreach($menuSubMenu as $k=>$submenu){
                    $submenu['id'] = $submenu['id'] ?? '_'.$k;
                    if(!empty($submenu['parent']) and isset($menus[$submenu['parent']])){
                        $menus[$submenu['parent']]['children'][$submenu['id']] = $submenu;
                        $menus[$submenu['parent']]['children'] = array_values(\Illuminate\Support\Arr::sort($menus[$submenu['parent']]['children'], function ($value) {
                            return $value['position'] ?? 100;
                        }));
                    }
                }
            }
        }
    }
}

// Custom Menu
$custom_modules = \Custom\ServiceProvider::getModules();
if(!empty($custom_modules)){
    foreach($custom_modules as $module){
        $moduleClass = "\\Custom\\".ucfirst($module)."\\ModuleProvider";
        if(class_exists($moduleClass))
        {
            $menuConfig = call_user_func([$moduleClass,'getAdminMenu']);

            if(!empty($menuConfig)){
                $menus = array_merge($menus,$menuConfig);
            }

            $menuSubMenu = call_user_func([$moduleClass,'getAdminSubMenu']);

            if(!empty($menuSubMenu)){
                foreach($menuSubMenu as $k=>$submenu){
                    $submenu['id'] = $submenu['id'] ?? '_'.$k;
                    if(!empty($submenu['parent']) and isset($menus[$submenu['parent']])){
                        $menus[$submenu['parent']]['children'][$submenu['id']] = $submenu;
                        $menus[$submenu['parent']]['children'] = array_values(\Illuminate\Support\Arr::sort($menus[$submenu['parent']]['children'], function ($value) {
                            return $value['position'] ?? 100;
                        }));
                    }
                }

            }
        }

    }
}
$typeManager = app()->make(\Modules\Type\TypeManager::class);
$menuConfig = $typeManager->adminMenus();

$menus = array_merge($menus,$menuConfig);


$currentUrl = url(\Modules\Core\Walkers\MenuWalker::getActiveMenu());
$user = \Illuminate\Support\Facades\Auth::user();
if (!empty($menus)){
    foreach ($menus as $k => $menuItem) {

        if (!empty($menuItem['permission']) and !$user->hasPermission($menuItem['permission'])) {
            unset($menus[$k]);
            continue;
        }
        $menus[$k]['class'] = $currentUrl == url($menuItem['url']) ? 'active' : '';
        if (!empty($menuItem['children'])) {
            $menus[$k]['class'] .= ' has-children';
            foreach ($menuItem['children'] as $k2 => $menuItem2) {
                if (!empty($menuItem2['permission']) and !$user->hasPermission($menuItem2['permission'])) {
                    unset($menus[$k]['children'][$k2]);
                    continue;
                }
                $menus[$k]['children'][$k2]['class'] = $currentUrl == url($menuItem2['url']) ? 'active' : '';
            }
        }
    }

    //@todo Sort Menu by Position
    $menus = array_values(\Illuminate\Support\Arr::sort($menus, function ($value) {
        return $value['position'] ?? 100;
    }));
}
?>
<!-- Sidebar Metronic moderne -->
<div class="h-full flex flex-col bg-gradient-to-b from-blue-600 via-purple-600 to-purple-700">

    <!-- Logo dans la sidebar -->
    <div class="flex items-center justify-center h-16 border-b border-white/20">
        <h2 class="text-xl font-bold text-white">DISCOVERY</h2>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        @foreach($menus as $menuItem)
            @php
                $isActive = strpos($menuItem['class'], 'active') !== false;
                $hasChildren = !empty($menuItem['children']);
                $menuId = 'menu-' . str_replace(['/', ' '], ['-', '-'], strtolower($menuItem['title']));
            @endphp

            <div class="menu-item">
                @if($hasChildren)
                    <!-- Menu avec sous-menus -->
                    <button onclick="toggleSubmenu('{{$menuId}}')" class="w-full flex items-center justify-between px-4 py-3 text-left text-white/90 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 group {{$isActive ? 'bg-white/20 text-white' : ''}}">
                        <div class="flex items-center space-x-3">
                            @if(!empty($menuItem['icon']))
                                <span class="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                                    <i class="{{str_replace('ion-ios-', 'fa fa-', $menuItem['icon'])}} text-sm"></i>
                                </span>
                            @endif
                            <span class="font-medium">
                                {!! clean($menuItem['title'], ['Attr.AllowedClasses'=>null]) !!}
                            </span>
                        </div>
                        <svg class="w-4 h-4 transform transition-transform duration-200" id="{{$menuId}}-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Sous-menus -->
                    <div id="{{$menuId}}" class="hidden mt-2 ml-8 space-y-1">
                        @foreach($menuItem['children'] as $menuItem2)
                            @php $isChildActive = strpos($menuItem2['class'] ?? '', 'active') !== false; @endphp
                            <a href="{{ url($menuItem2['url']) }}" class="flex items-center space-x-3 px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 {{$isChildActive ? 'bg-white/20 text-white' : ''}}">
                                @if(!empty($menuItem2['icon']))
                                    <i class="{{$menuItem2['icon']}} text-xs"></i>
                                @endif
                                <span>{!! clean($menuItem2['title'], ['Attr.AllowedClasses'=>null]) !!}</span>
                            </a>
                        @endforeach
                    </div>
                @else
                    <!-- Menu simple -->
                    <a href="{{ url($menuItem['url']) }}" class="flex items-center space-x-3 px-4 py-3 text-white/90 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 group {{$isActive ? 'bg-white/20 text-white' : ''}}">
                        @if(!empty($menuItem['icon']))
                            <span class="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                                @if(strpos($menuItem['icon'], 'ion-ios-') !== false)
                                    @switch($menuItem['icon'])
                                        @case('icon ion-ios-desktop')
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                            @break
                                        @case('icon ion-ios-apps')
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                            </svg>
                                            @break
                                        @case('icon ion-ios-cog')
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            @break
                                        @default
                                            <i class="{{$menuItem['icon']}} text-sm"></i>
                                    @endswitch
                                @else
                                    <i class="{{$menuItem['icon']}} text-sm"></i>
                                @endif
                            </span>
                        @endif
                        <span class="font-medium">
                            {!! clean($menuItem['title'], ['Attr.AllowedClasses'=>null]) !!}
                        </span>
                    </a>
                @endif
            </div>
        @endforeach
    </nav>

    <!-- Footer de la sidebar -->
    <div class="p-4 border-t border-white/20">
        <div class="text-center text-white/60 text-xs">
            <p>&copy; {{date('Y')}} Discovery Admin</p>
        </div>
    </div>
</div>

<!-- Script pour les sous-menus -->
<script>
function toggleSubmenu(menuId) {
    const submenu = document.getElementById(menuId);
    const arrow = document.getElementById(menuId + '-arrow');

    if (submenu.classList.contains('hidden')) {
        submenu.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        submenu.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
    }
}

// Auto-ouvrir les sous-menus actifs
document.addEventListener('DOMContentLoaded', function() {
    const activeItems = document.querySelectorAll('.menu-item a[class*="active"]');
    activeItems.forEach(item => {
        const submenu = item.closest('.menu-item').querySelector('[id^="menu-"]');
        if (submenu && submenu.id !== item.id) {
            submenu.classList.remove('hidden');
            const arrow = document.getElementById(submenu.id + '-arrow');
            if (arrow) arrow.style.transform = 'rotate(180deg)';
        }
    });
});
</script>
