<!-- Dialog Metronic moderne avec Tailwind CSS -->
<div id="cdn-browser-modal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <!-- Overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeBrowserModal()"></div>

    <!-- Dialog container -->
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <!-- Dialog panel -->
        <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-7xl">

            <!-- Header -->
            <div class="bg-white px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900" id="modal-title">
                        {{__('Media Browser')}}
                    </h3>
                    <button type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" onclick="closeBrowserModal()">
                        <span class="sr-only">{{__('Close')}}</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div id="cdn-browser" class="cdn-browser flex flex-col h-96 sm:h-[600px]" v-cloak :class="{is_loading:isLoading}">
                <!-- Toolbar Metronic -->
                <div class="files-nav flex-shrink-0 bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">

                        <!-- Left controls -->
                        <div class="flex flex-wrap items-center gap-3">
                            <div class="filter-item">
                                <div class="relative">
                                    <input type="text"
                                           placeholder="{{__("Search file name....")}}"
                                           class="block w-full rounded-md border-gray-300 pl-10 pr-3 py-2 text-sm placeholder-gray-500 focus:border-primary-500 focus:ring-primary-500"
                                           v-model="filter.s"
                                           @keyup.enter="filter.page = 1;reloadLists()">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-item">
                                <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                        @click="reloadAll()">
                                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                    {{__("Search")}}
                                </button>
                            </div>
                            <div class="filter-item">
                                <span class="text-sm text-gray-500">
                                    <strong>{{__("Total")}}:</strong> @{{total}} {{__("files")}}
                                </span>
                            </div>
                        </div>

                        <!-- Center controls - View type -->
                        <div class="flex items-center">
                            <div class="inline-flex rounded-md shadow-sm" role="group">
                                <button type="button"
                                        @click="setViewType('grid')"
                                        :class="viewType == 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
                                        class="px-3 py-2 text-sm font-medium border border-gray-300 rounded-l-md focus:z-10 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                    </svg>
                                </button>
                                <button type="button"
                                        @click="setViewType('list')"
                                        :class="viewType != 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
                                        class="px-3 py-2 text-sm font-medium border border-gray-300 rounded-r-md focus:z-10 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Right controls -->
                        <div class="flex flex-wrap items-center gap-3">
                            <!-- Loading spinner -->
                            <div v-show="isLoading" class="flex items-center">
                                <svg class="animate-spin h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>

                            <div class="filter-item">
                                <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                        @click="addFolder">
                                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    {{__("Add Folder")}}
                                </button>
                            </div>

                            <div class="filter-item">
                                <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 btn-pick-files relative overflow-hidden">
                                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                    </svg>
                                    {{__("Upload")}}
                                    <input multiple :accept="accept_type" type="file" name="files[]" ref="files" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Upload area -->
                <div class="upload-new bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg p-6 m-4" v-show="showUploader">
                    <input type="file" name="filepond[]" class="my-pond">
                </div>

                <!-- Files content -->
                <div class="files-list flex-1 overflow-y-auto">
                    <!-- Breadcrumb Metronic -->
                    <nav aria-label="breadcrumb" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                        <ol class="flex items-center space-x-2 text-sm">
                            <li>
                                <a @click="toFolderRoot" href="#" class="text-primary-600 hover:text-primary-700 font-medium">
                                    <svg class="h-4 w-4 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V7z" />
                                    </svg>
                                    {{__("Home")}}
                                </a>
                            </li>
                            <li v-for="(item,index) in breadcrumbs" :key="index" class="flex items-center">
                                <svg class="h-4 w-4 text-gray-400 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                                <a @click.prevent="showFolder(item,index)" href="#" class="text-primary-600 hover:text-primary-700 font-medium">
                                    @{{ item.name }}
                                </a>
                            </li>
                        </ol>
                    </nav>

                    <!-- List view -->
                    <div class="overflow-hidden" v-if="viewType == 'list'">
                        <!-- Table header -->
                        <div class="bg-gray-50 border-b border-gray-200">
                            <div class="grid grid-cols-12 gap-4 px-6 py-3 text-sm font-semibold text-gray-700">
                                <div class="col-span-6">{{__("Name")}}</div>
                                <div class="col-span-2">{{__("Type")}}</div>
                                <div class="col-span-2">{{__("Created At")}}</div>
                                <div class="col-span-2">{{__("Size")}}</div>
                            </div>
                        </div>
                        <!-- Table content -->
                        <div class="divide-y divide-gray-200">
                            <folder-item @deleted="deletedFolder" @toggle-edit="toggleEditFolder" @dblclick="showFolder(folder)" @update="updateFolder" :view-type="viewType" v-for="(folder,index) in folders" :index="index" :key="'folder-'+index" :folder="folder"></folder-item>
                            <file-item v-for="(file,index) in files" :key="index" :view-type="viewType" :selected="selected" :file="file" v-on:select-file="selectFile"></file-item>
                        </div>
                    </div>

                    <!-- Grid view -->
                    <div class="p-6" v-if="viewType == 'grid'">
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <folder-item @deleted="deletedFolder" @toggle-edit="toggleEditFolder" @dblclick="showFolder(folder)" @update="updateFolder" :view-type="viewType" v-for="(folder,index) in folders" :index="index" :key="'folder-'+index" :folder="folder"></folder-item>
                            <file-item v-for="(file,index) in files" :key="index" :view-type="viewType" :selected="selected" :file="file" v-on:select-file="selectFile"></file-item>
                        </div>
                    </div>
                    <!-- Empty state -->
                    <div class="flex flex-col items-center justify-center py-12" v-show="!total && apiFinished">
                        <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <p class="text-gray-500 text-lg">{{__("No file found")}}</p>
                    </div>

                    <!-- Pagination Metronic -->
                    <div class="flex items-center justify-center py-6 border-t border-gray-200" v-if="totalPage > 1">
                        <nav class="flex items-center space-x-2" aria-label="Pagination">
                            <!-- Previous button -->
                            <button :disabled="filter.page <= 1"
                                    :class="filter.page <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'"
                                    class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md"
                                    v-if="filter.page > 1"
                                    @click="changePage(filter.page-1,$event)">
                                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                {{__("Previous")}}
                            </button>
                            <span v-else class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                {{__("Previous")}}
                            </span>

                            <!-- Page numbers -->
                            <button v-for="p in totalPage"
                                    v-if="p >= (filter.page-3) && p <= (filter.page+3)"
                                    :key="p"
                                    :class="p == filter.page ? 'bg-primary-600 text-white border-primary-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                                    class="relative inline-flex items-center px-3 py-2 text-sm font-medium border rounded-md"
                                    @click="changePage(p,$event)">
                                @{{p}}
                            </button>

                            <!-- Next button -->
                            <button :disabled="filter.page >= totalPage"
                                    :class="filter.page >= totalPage ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'"
                                    class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md"
                                    v-if="filter.page < totalPage"
                                    @click="changePage(filter.page+1,$event)">
                                {{__("Next")}}
                                <svg class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                            <span v-else class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                                {{__("Next")}}
                                <svg class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </span>
                        </nav>
                    </div>
                </div>

                <!-- Footer actions -->
                <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 flex items-center justify-between" v-if="selected.length">
                    <div class="flex items-center space-x-4">
                        <button class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                @click="removeFiles"
                                v-if="selected && selected.length">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            {{__("Delete file")}}
                        </button>
                        <div class="text-sm text-gray-600" v-if="selected && selected.length">
                            <span class="font-medium">@{{selected.length}} {{__("file selected")}}</span>
                            <button @click="selected=[]" class="ml-2 text-primary-600 hover:text-primary-700 underline">
                                {{__("unselect")}}
                            </button>
                        </div>
                    </div>
                    <div>
                        <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                :class="{disabled:!selected.length}"
                                @click="sendFiles">
                            {{__("Use file")}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript pour le modal -->
<script>
function closeBrowserModal() {
    const modal = document.getElementById('cdn-browser-modal');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    }
}

function openBrowserModal() {
    const modal = document.getElementById('cdn-browser-modal');
    if (modal) {
        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }
}

// Fermer avec Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeBrowserModal();
    }
});
</script>
<script type="text/x-template" id="file-item-template">
    <div :class="viewType == 'grid'  ? 'file-item' : 'file-list-item'" >
        <div class="row hover:bg-f5f5f5 cursor-pointer" v-if="viewType == 'list'" @click="selectFile(file)" :class="{'active':selected.indexOf(file.id) !== -1}">
            <div class="col-sm-6 py-1 border-right border-bottom">
                <span v-html="getFileThumb(file)" class="mr-2 item-preview"></span> @{{ file.file_name }}</div>
            <div class="col-sm-2 py-1  border-right border-bottom">@{{file.file_extension}}</div>
            <div class="col-sm-2 py-1  border-right border-bottom">@{{file.created_at}}</div>
            <div class="col-sm-1 py-1 border-bottom">@{{humanFileSize(file.file_size)}}</div>
            <div class="col-sm-1 py-1  border-right border-bottom d-flex justify-content-end">
                <a :href="file.full_size" target="_blank" title="{{__("View file")}}"><i class=" fa fa-eye"></i></a>
            </div>
        </div>
        <div v-if="viewType == 'grid'" class="inner" :class="{active:selected.indexOf(file.id) !== -1 }" @click="selectFile(file)" :title="file.file_name">
            <div class="file-thumb" v-if="viewType=='grid'" v-html="getFileThumb(file)">
                </div>
                <div class="file-name">@{{file.file_name}}</div>
                <span class="file-checked-status" v-show="selected.indexOf(file.id) !== -1">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M186.301 339.893L96 249.461l-32 30.507L186.301 402 448 140.506 416 110z"/></svg>
            </span>
        </div>
    </div>
</script>
<script type="text/x-template" id="folder-item-template">
    <div :class="viewType == 'grid'  ? 'file-item folder-item' : 'folder-item'" @dblclick="$emit('dblclick',folder,index)">
        <div class="row hover:bg-f5f5f5 cursor-pointer" v-if="viewType == 'list'">
            <div class="col-sm-6 py-1 border-right border-bottom">
                <div class="d-flex justify-content-between">
                    <div class="d-flex flex-grow-1 align-items-center">
                        <div>
                            <img src="/icon/folder.png" width="20px" height="auto" class="mr-2 flex-shrink-0" alt="">
                        </div>
                        <div class="text-center font-weight-medium" v-if="!folder.onEdit">@{{folder.name}}</div>
                        <div class="" v-if="folder.onEdit">
                            <input ref="input" type="text" @blur="saveName" class="form-control" v-model="folder_name" >
                        </div>
                    </div>
                    <div>
                        <a href="#" class="btn-edit btn-sm position-absolute" @click.prevent="openEdit"><i class="fa fa-edit"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-sm-2 py-1 border-right border-bottom">{{__("Folder")}}</div>
            <div class="col-sm-2 py-1 border-right border-bottom">@{{ folder.created_at }}</div>
            <div class="col-sm-2 py-1 border-right border-bottom d-flex justify-content-end">
                <a href="#" class="btn-sm text-danger" title="{{__("Delete this folder")}}" @click.prevent="deleteFolder"><i class="fa fa-trash"></i></a>
            </div>
        </div>
        <div v-if="viewType == 'grid'" class="inner d-flex flex-column  position-relative">
            <div class="file-thumb flex-grow-1 d-flex align-items-center justify-content-center" style="background: #7b7d7e">
                <i class="fa fa-folder-o" style="font-size: 90px;color:white"></i>
            </div>
            <div class="text-center font-weight-medium p-2" v-if="!folder.onEdit">@{{folder.name}}</div>
            <div class="" v-if="folder.onEdit">
                <input ref="input" type="text" @blur="saveName" class="form-control" v-model="folder_name" >
            </div>
            <a href="#" class="btn-edit btn btn-sm btn-warning position-absolute top-0 right-0 m-2" @click.prevent="openEdit"><i class="fa fa-edit"></i></a>
        </div>
    </div>
</script>
