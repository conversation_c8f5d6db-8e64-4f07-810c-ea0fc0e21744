@if(!empty($breadcrumbs))
<!-- Breadcrumb Metronic moderne -->
<nav class="flex items-center space-x-2 text-sm text-gray-600 mb-6" aria-label="breadcrumb">
    <!-- Home -->
    <a href="{{route('admin.index')}}" class="flex items-center space-x-1 text-blue-600 hover:text-blue-700 transition-colors">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
        <span>{{__("Dashboard")}}</span>
    </a>

    @foreach($breadcrumbs as $breadcrumb)
        <!-- Séparateur -->
        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>

        <!-- Breadcrumb item -->
        <div class="{{$breadcrumb['class'] ?? ''}}">
            @if(!empty($breadcrumb['url']))
                <a href="{{url($breadcrumb['url'])}}" class="text-blue-600 hover:text-blue-700 transition-colors">
                    {{$breadcrumb['name']}}
                </a>
            @else
                <span class="text-gray-900 font-medium">{{$breadcrumb['name']}}</span>
            @endif
        </div>
    @endforeach
</nav>
@endif
