<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $page_title ?? 'Dashboard'}} - {{setting_item('site_title') ?? 'Booking Core'}}</title>

    @php
        $favicon = setting_item('site_favicon');
    @endphp
    @if($favicon)
        @php
            $file = (new \Modules\Media\Models\MediaFile())->findById($favicon);
        @endphp
        @if(!empty($file))
            <link rel="icon" type="{{$file['file_type']}}" href="{{asset('uploads/'.$file['file_path'])}}" />
        @else:
        <link rel="icon" type="image/png" href="{{url('images/favicon.png')}}" />
        @endif
    @endif

    <meta name="robots" content="noindex, nofollow" />
    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Styles -->
    <link href="{{ asset('libs/select2/css/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('libs/flags/css/flag-icon.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{url('libs/daterange/daterangepicker.css')}}"/>
    <!-- Bootstrap remplacé par Tailwind CSS pour un style Metronic moderne -->
    <link href="{{ asset('themes/admin/libs/font-awesome/css/font-awesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('dist/admin/css/app.css') }}" rel="stylesheet">

    <!-- Tailwind CSS avec configuration Metronic -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Couleurs Metronic
                        primary: {
                            50: '#f1faff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#009ef7', // Couleur principale Metronic
                            600: '#0077cc',
                            700: '#005fa3',
                            800: '#004785',
                            900: '#003366',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#50cd89', // Vert Metronic
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#ffc700', // Jaune Metronic
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#f1416c', // Rouge Metronic
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#181c32', // Couleur sombre Metronic
                        },
                        gray: {
                            50: '#f9f9f9',
                            100: '#f4f4f4',
                            200: '#e1e3ea',
                            300: '#d1d5db',
                            400: '#b5b5c3',
                            500: '#a1a5b7',
                            600: '#7e8299',
                            700: '#5e6278',
                            800: '#3f4254',
                            900: '#181c32',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
                    },
                    boxShadow: {
                        'metronic': '0 0 50px 0 rgba(82, 63, 105, 0.15)',
                        'metronic-lg': '0 0 50px 0 rgba(82, 63, 105, 0.25)',
                    },
                    borderRadius: {
                        'metronic': '0.75rem',
                    }
                }
            }
        }
    </script>

    <!-- Styles Metronic Template Authentiques -->
    <style>
        /* Variables CSS exactes du template Metronic */
        :root {
            --kt-primary: #009ef7;
            --kt-primary-light: #f1faff;
            --kt-primary-inverse: #ffffff;
            --kt-secondary: #e4e6ea;
            --kt-success: #50cd89;
            --kt-info: #7239ea;
            --kt-warning: #ffc700;
            --kt-danger: #f1416c;
            --kt-light: #f5f8fa;
            --kt-dark: #181c32;
            --kt-white: #ffffff;
            --kt-gray-100: #f9f9f9;
            --kt-gray-200: #f4f4f4;
            --kt-gray-300: #e1e3ea;
            --kt-gray-400: #b5b5c3;
            --kt-gray-500: #a1a5b7;
            --kt-gray-600: #7e8299;
            --kt-gray-700: #5e6278;
            --kt-gray-800: #3f4254;
            --kt-gray-900: #181c32;
            --kt-body-bg: #f5f8fa;
            --kt-body-color: #5e6278;
            --kt-border-color: #e1e3ea;
        }

        /* Reset et base Metronic */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            font-size: 13px;
            line-height: 1.6;
            color: var(--kt-body-color);
            background-color: var(--kt-body-bg);
            margin: 0;
            padding: 0;
        }

        /* Layout Metronic exact */
        .app-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .app-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: var(--kt-white);
            border-bottom: 1px solid var(--kt-border-color);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 25px;
            box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
        }

        .app-sidebar {
            position: fixed;
            top: 70px;
            left: 0;
            bottom: 0;
            width: 265px;
            background: var(--kt-white);
            border-right: 1px solid var(--kt-border-color);
            z-index: 999;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .app-main {
            margin-left: 265px;
            margin-top: 70px;
            min-height: calc(100vh - 70px);
            background: var(--kt-body-bg);
            padding: 25px;
        }

        .app-footer {
            background: var(--kt-white);
            border-top: 1px solid var(--kt-border-color);
            padding: 20px 25px;
            margin-left: 265px;
            color: var(--kt-gray-600);
            font-size: 12px;
        }

        /* Cards Metronic */
        .card {
            background: var(--kt-white);
            border: 1px solid var(--kt-border-color);
            border-radius: 0.75rem;
            box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
            margin-bottom: 25px;
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--kt-border-color);
            background: transparent;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--kt-gray-900);
            margin: 0;
        }

        .card-body {
            padding: 25px;
        }

        /* Buttons Metronic */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 10px 20px;
            font-size: 13px;
            font-weight: 600;
            border-radius: 0.475rem;
            border: 1px solid transparent;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.15s ease;
            min-height: 40px;
        }

        .btn-primary {
            background: var(--kt-primary);
            color: var(--kt-primary-inverse);
            border-color: var(--kt-primary);
        }

        .btn-primary:hover {
            background: #0095e8;
            border-color: #0095e8;
        }

        .btn-light {
            background: var(--kt-light);
            color: var(--kt-gray-700);
            border-color: var(--kt-light);
        }

        /* Tables Metronic */
        .table {
            width: 100%;
            margin-bottom: 0;
            color: var(--kt-gray-700);
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            border-bottom: 1px solid var(--kt-border-color);
            text-align: left;
        }

        .table th {
            font-weight: 600;
            color: var(--kt-gray-800);
            background: var(--kt-gray-100);
        }

        .table-hover tbody tr:hover {
            background: var(--kt-gray-100);
        }

        /* Badges Metronic */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 5px 10px;
            font-size: 11px;
            font-weight: 600;
            border-radius: 0.425rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .badge-success {
            background: rgba(80, 205, 137, 0.1);
            color: var(--kt-success);
        }

        .badge-warning {
            background: rgba(255, 199, 0, 0.1);
            color: var(--kt-warning);
        }

        .badge-danger {
            background: rgba(241, 65, 108, 0.1);
            color: var(--kt-danger);
        }

        /* Responsive Metronic */
        @media (max-width: 991.98px) {
            .app-sidebar {
                transform: translateX(-100%);
            }

            .app-main,
            .app-footer {
                margin-left: 0;
            }

            .app-sidebar.show {
                transform: translateX(0);
            }

            .app-sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 998;
                display: none;
            }

            .app-sidebar-overlay.show {
                display: block;
            }
        }

        /* Utilities Metronic */
        .d-flex { display: flex !important; }
        .align-items-center { align-items: center !important; }
        .justify-content-between { justify-content: space-between !important; }
        .text-muted { color: var(--kt-gray-600) !important; }
        .text-dark { color: var(--kt-gray-900) !important; }
        .fw-bold { font-weight: 600 !important; }
        .fs-6 { font-size: 13px !important; }
        .fs-5 { font-size: 15px !important; }
        .fs-4 { font-size: 18px !important; }
        .fs-3 { font-size: 22px !important; }
        .fs-2 { font-size: 28px !important; }
        .fs-1 { font-size: 36px !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-3 { margin-bottom: 15px !important; }
        .mb-5 { margin-bottom: 25px !important; }
        .me-3 { margin-right: 15px !important; }
        .ms-3 { margin-left: 15px !important; }
        .p-0 { padding: 0 !important; }
        .px-3 { padding-left: 15px !important; padding-right: 15px !important; }
        .py-3 { padding-top: 15px !important; padding-bottom: 15px !important; }
        .text-center { text-align: center !important; }
        .text-end { text-align: right !important; }
        .w-100 { width: 100% !important; }
        .h-100 { height: 100% !important; }

        /* Grid System Metronic */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-left: -12.5px;
            margin-right: -12.5px;
        }

        .col,
        .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
        .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
        .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
        .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
        .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
        .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
            position: relative;
            width: 100%;
            padding-left: 12.5px;
            padding-right: 12.5px;
        }

        .col-6 { flex: 0 0 50%; max-width: 50%; }
        .col-12 { flex: 0 0 100%; max-width: 100%; }

        @media (min-width: 768px) {
            .col-md-6 { flex: 0 0 50%; max-width: 50%; }
            .col-md-12 { flex: 0 0 100%; max-width: 100%; }
        }

        @media (min-width: 992px) {
            .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
            .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
        }

        .container-fluid {
            width: 100%;
            padding-left: 15px;
            padding-right: 15px;
            margin-left: auto;
            margin-right: auto;
        }
    </style>
    {!! \App\Helpers\Assets::css() !!}
    {!! \App\Helpers\Assets::js() !!}
    <script>
        var bookingCore  = {
            url:'{{url('/')}}',
            admin_url:'{{route('admin.index')}}',
            map_provider:'{{setting_item('map_provider')}}',
            map_gmap_key:'{{setting_item('map_gmap_key')}}',
            csrf:'{{csrf_token()}}',
            date_format:'{{get_moment_date_format()}}',
            markAsRead:'{{route('core.admin.notification.markAsRead')}}',
            markAllAsRead:'{{route('core.admin.notification.markAllAsRead')}}',
            loadNotify : '{{route('core.admin.notification.loadNotify')}}',
            pusher_api_key : '{{setting_item("pusher_api_key")}}',
            pusher_cluster : '{{setting_item("pusher_cluster")}}',
            isAdmin : {{is_admin() ? 1 : 0}},
            currentUser: {{(int)Auth::id()}},
            media:{
                groups:{!! json_encode(config('bc.media.groups')) !!},
            },
            language: '{{ app()->getLocale() }}',
        };
        var i18n = {
            warning:"{{__("Warning")}}",
            success:"{{__("Success")}}",
            confirm_delete:"{{__("Do you want to delete?")}}",
            confirm_recovery:"{{__("Do you want to restore?")}}",
            confirm:"{{__("Confirm")}}",
            cancel:"{{__("Cancel")}}",
            custom_range: "{{ __("Custom Range") }}",
            apply: "{{ __("Apply") }}"
        };
        var daterangepickerLocale = {
            "applyLabel": "{{__('Apply')}}",
            "cancelLabel": "{{__('Cancel')}}",
            "fromLabel": "{{__('From')}}",
            "toLabel": "{{__('To')}}",
            "customRangeLabel": "{{__('Custom')}}",
            "weekLabel": "{{__('W')}}",
            "first_day_of_week": {{ setting_item("site_first_day_of_the_weekin_calendar","1") }},
            "daysOfWeek": [
                "{{__('Su')}}",
                "{{__('Mo')}}",
                "{{__('Tu')}}",
                "{{__('We')}}",
                "{{__('Th')}}",
                "{{__('Fr')}}",
                "{{__('Sa')}}"
            ],
            "monthNames": [
                "{{__('January')}}",
                "{{__('February')}}",
                "{{__('March')}}",
                "{{__('April')}}",
                "{{__('May')}}",
                "{{__('June')}}",
                "{{__('July')}}",
                "{{__('August')}}",
                "{{__('September')}}",
                "{{__('October')}}",
                "{{__('November')}}",
                "{{__('December')}}"
            ],
        };

        var image_editer = {
            language: '{{ app()->getLocale() }}',
            translations: {
                {{ app()->getLocale() }}: {
                    'header.image_editor_title': '{{ __('Image Editor') }}',
                    'header.toggle_fullscreen': '{{ __('Toggle fullscreen') }}',
                    'header.close': '{{ __('Close') }}',
                    'header.close_modal': '{{ __('Close window') }}',
                    'toolbar.download': '{{ __('Save Change') }}',
                    'toolbar.save': '{{ __('Save') }}',
                    'toolbar.apply': '{{ __('Apply') }}',
                    'toolbar.saveAsNewImage': '{{ __('Save As New Image') }}',
                    'toolbar.cancel': '{{ __('Cancel') }}',
                    'toolbar.go_back': '{{ __('Go Back') }}',
                    'toolbar.adjust': '{{ __('Adjust') }}',
                    'toolbar.effects': '{{ __('Effects') }}',
                    'toolbar.filters': '{{ __('Filters') }}',
                    'toolbar.orientation': '{{ __('Orientation') }}',
                    'toolbar.crop': '{{ __('Crop') }}',
                    'toolbar.resize': '{{ __('Resize') }}',
                    'toolbar.watermark': '{{ __('Watermark') }}',
                    'toolbar.focus_point': '{{ __('Focus point') }}',
                    'toolbar.shapes': '{{ __('Shapes') }}',
                    'toolbar.image': '{{ __('Image') }}',
                    'toolbar.text': '{{ __('Text') }}',
                    'adjust.brightness': '{{ __('Brightness') }}',
                    'adjust.contrast': '{{ __('Contrast') }}',
                    'adjust.exposure': '{{ __('Exposure') }}',
                    'adjust.saturation': '{{ __('Saturation') }}',
                    'orientation.rotate_l': '{{ __('Rotate Left') }}',
                    'orientation.rotate_r': '{{ __('Rotate Right') }}',
                    'orientation.flip_h': '{{ __('Flip Horizontally') }}',
                    'orientation.flip_v': '{{ __('Flip Vertically') }}',
                    'pre_resize.title': '{{ __('Would you like to reduce resolution before editing the image?') }}',
                    'pre_resize.keep_original_resolution': '{{ __('Keep original resolution') }}',
                    'pre_resize.resize_n_continue': '{{ __('Resize & Continue') }}',
                    'footer.reset': '{{ __('Reset') }}',
                    'footer.undo': '{{ __('Undo') }}',
                    'footer.redo': '{{ __('Redo') }}',
                    'spinner.label': '{{ __('Processing...') }}',
                    'warning.too_big_resolution': '{{ __('The resolution of the image is too big for the web. It can cause problems with Image Editor performance.') }}',
                    'common.x': '{{ __('x') }}',
                    'common.y': '{{ __('y') }}',
                    'common.width': '{{ __('width') }}',
                    'common.height': '{{ __('height') }}',
                    'common.custom': '{{ __('custom') }}',
                    'common.original': '{{ __('original') }}',
                    'common.square': '{{ __('square') }}',
                    'common.opacity': '{{ __('Opacity') }}',
                    'common.apply_watermark': '{{ __('Apply watermark') }}',
                    'common.url': '{{ __('URL') }}',
                    'common.upload': '{{ __('Upload') }}',
                    'common.gallery': '{{ __('Gallery') }}',
                    'common.text': '{{ __('Text') }}',
                }
            }
        };
    </script>
    <script src="{{ asset('libs/tinymce/js/tinymce/tinymce.min.js') }}" ></script>
    @stack('css')

</head>
<body class="{{($enable_multi_lang ?? '') ? 'enable_multi_lang' : '' }} @if(setting_item('site_enable_multi_lang')) site_enable_multi_lang @endif">

<!-- Structure exacte du template Metronic -->
<div id="app" class="app-wrapper">

    <!-- Header Metronic Template -->
    <div class="app-header">
        @include('Layout::admin.parts.header')
    </div>

    <!-- Sidebar Metronic Template -->
    <div class="app-sidebar" id="kt_app_sidebar">
        @include('Layout::admin.parts.sidebar')
    </div>

    <!-- Main Content Metronic Template -->
    <div class="app-main">
        <!-- Breadcrumb -->
        @include('Layout::admin.parts.bc')

        <!-- Page Content -->
        @yield('content')
    </div>

    <!-- Footer Metronic Template -->
    <div class="app-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                {{date('Y')}} &copy; {{__('Booking Core by')}}
                <a href="{{__('https://www.bookingcore.co')}}" target="_blank" style="color: var(--kt-primary);">
                    {{__('BookingCore Team')}}
                </a>
            </div>
            <div class="text-muted">
                <a href="{{__('https://www.bookingcore.co')}}" target="_blank" class="text-muted me-3">{{__('About Us')}}</a>
                <a href="{{__('https://m.me/bookingcore')}}" target="_blank" class="text-muted">{{__('Contact Us')}}</a>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="app-sidebar-overlay" id="kt_app_sidebar_overlay"></div>
</div>

@include('Media::browser')

<!-- Scripts -->
{!! \App\Helpers\Assets::css(true) !!}
<script src="{{ asset('libs/pusher.min.js') }}"></script>
<script src="{{ asset('dist/admin/js/manifest.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/jquery-3.6.3.min.js?_ver='.config('app.asset_version')) }}" ></script>
<!-- JavaScript Metronic Template -->
<script>
"use strict";

// Classe principale Metronic
var KTApp = function() {
    var initSidebar = function() {
        var sidebar = document.getElementById('kt_app_sidebar');
        var overlay = document.getElementById('kt_app_sidebar_overlay');
        var toggleBtn = document.querySelector('.btn-toggle-admin-menu');

        if (toggleBtn) {
            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();

                if (window.innerWidth < 992) {
                    sidebar.classList.toggle('show');
                    overlay.classList.toggle('show');
                }
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            });
        }

        // Fermer sidebar sur resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            }
        });
    };

    var initDropdowns = function() {
        document.querySelectorAll('[data-toggle="dropdown"]').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var menu = this.nextElementSibling;
                if (menu && menu.classList.contains('dropdown-menu')) {
                    // Fermer tous les autres dropdowns
                    document.querySelectorAll('.dropdown-menu').forEach(function(otherMenu) {
                        if (otherMenu !== menu) {
                            otherMenu.style.display = 'none';
                        }
                    });

                    // Toggle le dropdown actuel
                    if (menu.style.display === 'block') {
                        menu.style.display = 'none';
                    } else {
                        menu.style.display = 'block';
                    }
                }
            });
        });

        // Fermer dropdowns en cliquant à l'extérieur
        document.addEventListener('click', function(e) {
            if (!e.target.closest('[data-toggle="dropdown"]')) {
                document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                    menu.style.display = 'none';
                });
            }
        });
    };

    var initCards = function() {
        // Animation d'entrée pour les cartes
        var cards = document.querySelectorAll('.card');
        cards.forEach(function(card, index) {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(function() {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    };

    return {
        init: function() {
            initSidebar();
            initDropdowns();
            initCards();
        }
    };
}();

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    KTApp.init();
});

// Fonctions globales Metronic
window.KTApp = KTApp;
</script>
<script src="{{ asset('dist/admin/js/vendor.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/filerobot-image-editor/filerobot-image-editor.min.js?_ver='.config('app.asset_version')) }}"></script>

<script src="{{ asset('dist/admin/js/app.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/vue/vue'.(!env('APP_DEBUG') ? '.min':'').'.js') }}"></script>

<script src="{{ asset('libs/select2/js/select2.min.js') }}" ></script>
<script src="{{ asset('libs/bootbox/bootbox.min.js') }}"></script>

<script src="{{url('libs/daterange/moment.min.js')}}"></script>
<script src="{{url('libs/daterange/daterangepicker.min.js?_ver='.config('app.asset_version'))}}"></script>

{!! \App\Helpers\Assets::js(true) !!}

@stack('js')

</body>
</html>
