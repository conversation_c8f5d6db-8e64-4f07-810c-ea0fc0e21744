<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $page_title ?? 'Dashboard'}} - {{setting_item('site_title') ?? 'Booking Core'}}</title>

    @php
        $favicon = setting_item('site_favicon');
    @endphp
    @if($favicon)
        @php
            $file = (new \Modules\Media\Models\MediaFile())->findById($favicon);
        @endphp
        @if(!empty($file))
            <link rel="icon" type="{{$file['file_type']}}" href="{{asset('uploads/'.$file['file_path'])}}" />
        @else:
        <link rel="icon" type="image/png" href="{{url('images/favicon.png')}}" />
        @endif
    @endif

    <meta name="robots" content="noindex, nofollow" />
    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Styles -->
    <link href="{{ asset('libs/select2/css/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('libs/flags/css/flag-icon.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{url('libs/daterange/daterangepicker.css')}}"/>
    <!-- Bootstrap supprimé pour éviter les conflits avec Tailwind -->
    <!-- <link href="{{ asset('themes/admin/libs/bootstrap-4.6.2-dist/css/bootstrap.min.css') }}" rel="stylesheet"> -->
    <link href="{{ asset('themes/admin/libs/font-awesome/css/font-awesome.min.css') }}" rel="stylesheet">
    <!-- CSS admin personnalisé supprimé pour utiliser uniquement Tailwind -->
    <!-- <link href="{{ asset('dist/admin/css/app.css') }}" rel="stylesheet"> -->

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    }
                }
            }
        }
    </script>
    {!! \App\Helpers\Assets::css() !!}
    {!! \App\Helpers\Assets::js() !!}
    <script>
        var bookingCore  = {
            url:'{{url('/')}}',
            admin_url:'{{route('admin.index')}}',
            map_provider:'{{setting_item('map_provider')}}',
            map_gmap_key:'{{setting_item('map_gmap_key')}}',
            csrf:'{{csrf_token()}}',
            date_format:'{{get_moment_date_format()}}',
            markAsRead:'{{route('core.admin.notification.markAsRead')}}',
            markAllAsRead:'{{route('core.admin.notification.markAllAsRead')}}',
            loadNotify : '{{route('core.admin.notification.loadNotify')}}',
            pusher_api_key : '{{setting_item("pusher_api_key")}}',
            pusher_cluster : '{{setting_item("pusher_cluster")}}',
            isAdmin : {{is_admin() ? 1 : 0}},
            currentUser: {{(int)Auth::id()}},
            media:{
                groups:{!! json_encode(config('bc.media.groups')) !!},
            },
            language: '{{ app()->getLocale() }}',
        };
        var i18n = {
            warning:"{{__("Warning")}}",
            success:"{{__("Success")}}",
            confirm_delete:"{{__("Do you want to delete?")}}",
            confirm_recovery:"{{__("Do you want to restore?")}}",
            confirm:"{{__("Confirm")}}",
            cancel:"{{__("Cancel")}}",
            custom_range: "{{ __("Custom Range") }}",
            apply: "{{ __("Apply") }}"
        };
        var daterangepickerLocale = {
            "applyLabel": "{{__('Apply')}}",
            "cancelLabel": "{{__('Cancel')}}",
            "fromLabel": "{{__('From')}}",
            "toLabel": "{{__('To')}}",
            "customRangeLabel": "{{__('Custom')}}",
            "weekLabel": "{{__('W')}}",
            "first_day_of_week": {{ setting_item("site_first_day_of_the_weekin_calendar","1") }},
            "daysOfWeek": [
                "{{__('Su')}}",
                "{{__('Mo')}}",
                "{{__('Tu')}}",
                "{{__('We')}}",
                "{{__('Th')}}",
                "{{__('Fr')}}",
                "{{__('Sa')}}"
            ],
            "monthNames": [
                "{{__('January')}}",
                "{{__('February')}}",
                "{{__('March')}}",
                "{{__('April')}}",
                "{{__('May')}}",
                "{{__('June')}}",
                "{{__('July')}}",
                "{{__('August')}}",
                "{{__('September')}}",
                "{{__('October')}}",
                "{{__('November')}}",
                "{{__('December')}}"
            ],
        };

        var image_editer = {
            language: '{{ app()->getLocale() }}',
            translations: {
                {{ app()->getLocale() }}: {
                    'header.image_editor_title': '{{ __('Image Editor') }}',
                    'header.toggle_fullscreen': '{{ __('Toggle fullscreen') }}',
                    'header.close': '{{ __('Close') }}',
                    'header.close_modal': '{{ __('Close window') }}',
                    'toolbar.download': '{{ __('Save Change') }}',
                    'toolbar.save': '{{ __('Save') }}',
                    'toolbar.apply': '{{ __('Apply') }}',
                    'toolbar.saveAsNewImage': '{{ __('Save As New Image') }}',
                    'toolbar.cancel': '{{ __('Cancel') }}',
                    'toolbar.go_back': '{{ __('Go Back') }}',
                    'toolbar.adjust': '{{ __('Adjust') }}',
                    'toolbar.effects': '{{ __('Effects') }}',
                    'toolbar.filters': '{{ __('Filters') }}',
                    'toolbar.orientation': '{{ __('Orientation') }}',
                    'toolbar.crop': '{{ __('Crop') }}',
                    'toolbar.resize': '{{ __('Resize') }}',
                    'toolbar.watermark': '{{ __('Watermark') }}',
                    'toolbar.focus_point': '{{ __('Focus point') }}',
                    'toolbar.shapes': '{{ __('Shapes') }}',
                    'toolbar.image': '{{ __('Image') }}',
                    'toolbar.text': '{{ __('Text') }}',
                    'adjust.brightness': '{{ __('Brightness') }}',
                    'adjust.contrast': '{{ __('Contrast') }}',
                    'adjust.exposure': '{{ __('Exposure') }}',
                    'adjust.saturation': '{{ __('Saturation') }}',
                    'orientation.rotate_l': '{{ __('Rotate Left') }}',
                    'orientation.rotate_r': '{{ __('Rotate Right') }}',
                    'orientation.flip_h': '{{ __('Flip Horizontally') }}',
                    'orientation.flip_v': '{{ __('Flip Vertically') }}',
                    'pre_resize.title': '{{ __('Would you like to reduce resolution before editing the image?') }}',
                    'pre_resize.keep_original_resolution': '{{ __('Keep original resolution') }}',
                    'pre_resize.resize_n_continue': '{{ __('Resize & Continue') }}',
                    'footer.reset': '{{ __('Reset') }}',
                    'footer.undo': '{{ __('Undo') }}',
                    'footer.redo': '{{ __('Redo') }}',
                    'spinner.label': '{{ __('Processing...') }}',
                    'warning.too_big_resolution': '{{ __('The resolution of the image is too big for the web. It can cause problems with Image Editor performance.') }}',
                    'common.x': '{{ __('x') }}',
                    'common.y': '{{ __('y') }}',
                    'common.width': '{{ __('width') }}',
                    'common.height': '{{ __('height') }}',
                    'common.custom': '{{ __('custom') }}',
                    'common.original': '{{ __('original') }}',
                    'common.square': '{{ __('square') }}',
                    'common.opacity': '{{ __('Opacity') }}',
                    'common.apply_watermark': '{{ __('Apply watermark') }}',
                    'common.url': '{{ __('URL') }}',
                    'common.upload': '{{ __('Upload') }}',
                    'common.gallery': '{{ __('Gallery') }}',
                    'common.text': '{{ __('Text') }}',
                }
            }
        };
    </script>
    <script src="{{ asset('libs/tinymce/js/tinymce/tinymce.min.js') }}" ></script>
    @stack('css')

</head>
<body class="{{($enable_multi_lang ?? '') ? 'enable_multi_lang' : '' }} @if(setting_item('site_enable_multi_lang')) site_enable_multi_lang @endif bg-gray-50">

<!-- Styles Metronic modernes -->
<style>
    /* Variables CSS pour le thème */
    :root {
        --kt-primary: #009ef7;
        --kt-primary-light: #f1faff;
        --kt-primary-inverse: #ffffff;
        --kt-secondary: #e4e6ea;
        --kt-success: #50cd89;
        --kt-info: #7239ea;
        --kt-warning: #ffc700;
        --kt-danger: #f1416c;
        --kt-light: #f5f8fa;
        --kt-dark: #181c32;
        --kt-gray-100: #f9f9f9;
        --kt-gray-200: #f4f4f4;
        --kt-gray-300: #e1e3ea;
        --kt-gray-400: #b5b5c3;
        --kt-gray-500: #a1a5b7;
        --kt-gray-600: #7e8299;
        --kt-gray-700: #5e6278;
        --kt-gray-800: #3f4254;
        --kt-gray-900: #181c32;
    }

    /* Sidebar moderne */
    .metronic-sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    }

    /* Header moderne */
    .metronic-header {
        background: #ffffff;
        border-bottom: 1px solid #e4e6ea;
        box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
    }

    /* Content area */
    .metronic-content {
        background: #f5f8fa;
        min-height: calc(100vh - 70px);
    }

    /* Cards modernes */
    .metronic-card {
        background: #ffffff;
        border-radius: 0.75rem;
        box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
        border: 1px solid #e4e6ea;
    }

    /* Animations */
    .fade-in {
        animation: fadeIn 0.3s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Remplacement des composants Bootstrap */
    .table {
        @apply w-full text-sm text-left text-gray-500;
    }

    .table thead {
        @apply text-xs text-gray-700 uppercase bg-gray-50;
    }

    .table th {
        @apply px-6 py-3;
    }

    .table td {
        @apply px-6 py-4 whitespace-nowrap;
    }

    .table-hover tbody tr:hover {
        @apply bg-gray-50;
    }

    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-success {
        @apply bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-danger {
        @apply bg-red-100 text-red-800;
    }

    .badge-info {
        @apply bg-blue-100 text-blue-800;
    }

    .badge-secondary {
        @apply bg-gray-100 text-gray-800;
    }

    .btn {
        @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-colors;
    }

    .btn-primary {
        @apply text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    }

    .btn-secondary {
        @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    }

    .btn-success {
        @apply text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500;
    }

    .btn-danger {
        @apply text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500;
    }

    .btn-link {
        @apply text-blue-600 hover:text-blue-700 underline bg-transparent border-transparent shadow-none;
    }

    .form-control {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
    }

    .form-group {
        @apply mb-4;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-1;
    }

    .alert {
        @apply p-4 rounded-md;
    }

    .alert-success {
        @apply bg-green-50 border border-green-200 text-green-800;
    }

    .alert-warning {
        @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
    }

    .alert-danger {
        @apply bg-red-50 border border-red-200 text-red-800;
    }

    .alert-info {
        @apply bg-blue-50 border border-blue-200 text-blue-800;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .metronic-sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .metronic-sidebar.show {
            transform: translateX(0);
        }
    }
</style>

<div id="app" class="min-h-screen bg-gray-50">
    <!-- Layout principal Metronic -->
    <div class="flex h-screen bg-gray-50">

        <!-- Sidebar -->
        <div class="metronic-sidebar w-64 fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0" id="sidebar">
            @include('Layout::admin.parts.sidebar')
        </div>

        <!-- Overlay pour mobile -->
        <div class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden" id="sidebar-overlay"></div>

        <!-- Contenu principal -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">

            <!-- Header -->
            <header class="metronic-header bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                @include('Layout::admin.parts.header')
            </header>

            <!-- Zone de contenu -->
            <main class="metronic-content flex-1 overflow-x-hidden overflow-y-auto p-6">

                <!-- Breadcrumb -->
                <div class="mb-6">
                    @include('Layout::admin.parts.bc')
                </div>

                <!-- Contenu de la page -->
                <div class="fade-in">
                    @yield('content')
                </div>

            </main>

            <!-- Footer moderne -->
            <footer class="bg-white border-t border-gray-200 px-6 py-4 flex-shrink-0">
                <div class="flex flex-col sm:flex-row justify-between items-center">
                    <div class="text-sm text-gray-600 mb-2 sm:mb-0">
                        {{date('Y')}} &copy; {{__('Booking Core by')}}
                        <a href="{{__('https://www.bookingcore.co')}}" target="_blank" class="text-primary-600 hover:text-primary-700 font-medium">
                            {{__('BookingCore Team')}}
                        </a>
                    </div>
                    <div class="flex space-x-4 text-sm">
                        <a href="{{__('https://www.bookingcore.co')}}" target="_blank" class="text-gray-600 hover:text-primary-600 transition-colors">{{__('About Us')}}</a>
                        <a href="{{__('https://m.me/bookingcore')}}" target="_blank" class="text-gray-600 hover:text-primary-600 transition-colors">{{__('Contact Us')}}</a>
                    </div>
                </div>
            </footer>

        </div>
    </div>
</div>

<!-- Scripts pour remplacer Bootstrap -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sidebar mobile
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');
    const toggleBtn = document.querySelector('.btn-toggle-admin-menu');

    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            overlay.classList.toggle('hidden');
        });
    }

    if (overlay) {
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('show');
            overlay.classList.add('hidden');
        });
    }

    // Remplacement des dropdowns Bootstrap
    document.querySelectorAll('[data-toggle="dropdown"]').forEach(function(trigger) {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdown = trigger.nextElementSibling;
            if (dropdown && dropdown.classList.contains('dropdown-menu')) {
                // Fermer tous les autres dropdowns
                document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                    if (menu !== dropdown) {
                        menu.classList.add('hidden');
                    }
                });

                // Toggle le dropdown actuel
                dropdown.classList.toggle('hidden');
            }
        });
    });

    // Fermer les dropdowns en cliquant à l'extérieur
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[data-toggle="dropdown"]')) {
            document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                menu.classList.add('hidden');
            });
        }
    });

    // Remplacement des modals Bootstrap (basique)
    window.showModal = function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }
    };

    window.hideModal = function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    };

    // Fermer les modals avec Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.modal').forEach(function(modal) {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            });
        }
    });

    // Remplacement des tooltips Bootstrap (basique)
    document.querySelectorAll('[data-toggle="tooltip"]').forEach(function(element) {
        element.addEventListener('mouseenter', function() {
            const title = this.getAttribute('title') || this.getAttribute('data-original-title');
            if (title) {
                const tooltip = document.createElement('div');
                tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg';
                tooltip.textContent = title;
                tooltip.style.top = '-30px';
                tooltip.style.left = '50%';
                tooltip.style.transform = 'translateX(-50%)';
                this.style.position = 'relative';
                this.appendChild(tooltip);
            }
        });

        element.addEventListener('mouseleave', function() {
            const tooltip = this.querySelector('.absolute.z-50');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
});
</script>

@include('Media::browser')

<!-- Scripts -->
{!! \App\Helpers\Assets::css(true) !!}
<script src="{{ asset('libs/pusher.min.js') }}"></script>
<script src="{{ asset('dist/admin/js/manifest.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/jquery-3.6.3.min.js?_ver='.config('app.asset_version')) }}" ></script>
<!-- Bootstrap JS supprimé pour éviter les conflits avec Tailwind -->
<!-- <script src="{{ asset('themes/admin/libs/bootstrap-4.6.2-dist/js/bootstrap.bundle.min.js?_ver='.config('app.asset_version')) }}" ></script> -->
<script src="{{ asset('dist/admin/js/vendor.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/filerobot-image-editor/filerobot-image-editor.min.js?_ver='.config('app.asset_version')) }}"></script>

<script src="{{ asset('dist/admin/js/app.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/vue/vue'.(!env('APP_DEBUG') ? '.min':'').'.js') }}"></script>

<script src="{{ asset('libs/select2/js/select2.min.js') }}" ></script>
<script src="{{ asset('libs/bootbox/bootbox.min.js') }}"></script>

<script src="{{url('libs/daterange/moment.min.js')}}"></script>
<script src="{{url('libs/daterange/daterangepicker.min.js?_ver='.config('app.asset_version'))}}"></script>

{!! \App\Helpers\Assets::js(true) !!}

@stack('js')

</body>
</html>
