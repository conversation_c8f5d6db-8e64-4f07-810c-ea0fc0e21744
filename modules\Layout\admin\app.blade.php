<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $page_title ?? 'Dashboard'}} - {{setting_item('site_title') ?? 'Booking Core'}}</title>

    @php
        $favicon = setting_item('site_favicon');
    @endphp
    @if($favicon)
        @php
            $file = (new \Modules\Media\Models\MediaFile())->findById($favicon);
        @endphp
        @if(!empty($file))
            <link rel="icon" type="{{$file['file_type']}}" href="{{asset('uploads/'.$file['file_path'])}}" />
        @else:
        <link rel="icon" type="image/png" href="{{url('images/favicon.png')}}" />
        @endif
    @endif

    <meta name="robots" content="noindex, nofollow" />
    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Styles -->
    <link href="{{ asset('libs/select2/css/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('libs/flags/css/flag-icon.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{url('libs/daterange/daterangepicker.css')}}"/>
    <!-- Bootstrap remplacé par Tailwind CSS pour un style Metronic moderne -->
    <link href="{{ asset('themes/admin/libs/font-awesome/css/font-awesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('dist/admin/css/app.css') }}" rel="stylesheet">

    <!-- Tailwind CSS avec configuration Metronic -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Couleurs Metronic
                        primary: {
                            50: '#f1faff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#009ef7', // Couleur principale Metronic
                            600: '#0077cc',
                            700: '#005fa3',
                            800: '#004785',
                            900: '#003366',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#50cd89', // Vert Metronic
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#ffc700', // Jaune Metronic
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#f1416c', // Rouge Metronic
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#181c32', // Couleur sombre Metronic
                        },
                        gray: {
                            50: '#f9f9f9',
                            100: '#f4f4f4',
                            200: '#e1e3ea',
                            300: '#d1d5db',
                            400: '#b5b5c3',
                            500: '#a1a5b7',
                            600: '#7e8299',
                            700: '#5e6278',
                            800: '#3f4254',
                            900: '#181c32',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
                    },
                    boxShadow: {
                        'metronic': '0 0 50px 0 rgba(82, 63, 105, 0.15)',
                        'metronic-lg': '0 0 50px 0 rgba(82, 63, 105, 0.25)',
                    },
                    borderRadius: {
                        'metronic': '0.75rem',
                    }
                }
            }
        }
    </script>

    <!-- Styles Metronic personnalisés -->
    <style>
        /* Variables CSS Metronic */
        :root {
            --kt-primary: #009ef7;
            --kt-primary-light: #f1faff;
            --kt-success: #50cd89;
            --kt-warning: #ffc700;
            --kt-danger: #f1416c;
            --kt-dark: #181c32;
            --kt-gray-100: #f9f9f9;
            --kt-gray-200: #f4f4f4;
            --kt-gray-300: #e1e3ea;
            --kt-gray-400: #b5b5c3;
            --kt-gray-500: #a1a5b7;
            --kt-gray-600: #7e8299;
            --kt-gray-700: #5e6278;
            --kt-gray-800: #3f4254;
            --kt-gray-900: #181c32;
        }

        /* Styles de base Metronic avec Tailwind */
        body {
            @apply bg-gray-50 font-sans text-gray-700;
        }

        /* Composants Metronic */
        .metronic-card {
            @apply bg-white rounded-metronic shadow-metronic border border-gray-200;
        }

        .metronic-btn-primary {
            @apply bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md;
        }

        .metronic-btn-secondary {
            @apply bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-all duration-200;
        }

        /* Remplacement des classes Bootstrap par Tailwind */
        .container-fluid {
            @apply w-full px-4;
        }

        .row {
            @apply flex flex-wrap -mx-2;
        }

        .col-md-6 {
            @apply w-full md:w-1/2 px-2;
        }

        .col-md-12 {
            @apply w-full px-2;
        }

        .col-lg-6 {
            @apply w-full lg:w-1/2 px-2;
        }

        .d-flex {
            @apply flex;
        }

        .justify-content-between {
            @apply justify-between;
        }

        .align-items-center {
            @apply items-center;
        }

        .text-md-right {
            @apply md:text-right;
        }

        .d-none {
            @apply hidden;
        }

        .d-sm-block {
            @apply sm:block;
        }

        .mb-3 {
            @apply mb-3;
        }

        .text-uppercase {
            @apply uppercase;
        }

        /* Styles pour les composants existants */
        .main-header {
            @apply bg-white shadow-sm border-b border-gray-200 h-16 fixed top-0 left-0 right-0 z-50;
        }

        .main-sidebar {
            @apply fixed left-0 top-16 bottom-0 w-64 bg-white border-r border-gray-200 overflow-y-auto z-40;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .main-content {
            @apply ml-64 mt-16 min-h-screen bg-gray-50 p-6;
        }

        .main-footer {
            @apply bg-white border-t border-gray-200 px-6 py-4 text-sm text-gray-600;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-sidebar {
                @apply -translate-x-full transition-transform duration-300;
            }

            .main-content {
                @apply ml-0;
            }

            .sidebar-open .main-sidebar {
                @apply translate-x-0;
            }
        }
    </style>
    {!! \App\Helpers\Assets::css() !!}
    {!! \App\Helpers\Assets::js() !!}
    <script>
        var bookingCore  = {
            url:'{{url('/')}}',
            admin_url:'{{route('admin.index')}}',
            map_provider:'{{setting_item('map_provider')}}',
            map_gmap_key:'{{setting_item('map_gmap_key')}}',
            csrf:'{{csrf_token()}}',
            date_format:'{{get_moment_date_format()}}',
            markAsRead:'{{route('core.admin.notification.markAsRead')}}',
            markAllAsRead:'{{route('core.admin.notification.markAllAsRead')}}',
            loadNotify : '{{route('core.admin.notification.loadNotify')}}',
            pusher_api_key : '{{setting_item("pusher_api_key")}}',
            pusher_cluster : '{{setting_item("pusher_cluster")}}',
            isAdmin : {{is_admin() ? 1 : 0}},
            currentUser: {{(int)Auth::id()}},
            media:{
                groups:{!! json_encode(config('bc.media.groups')) !!},
            },
            language: '{{ app()->getLocale() }}',
        };
        var i18n = {
            warning:"{{__("Warning")}}",
            success:"{{__("Success")}}",
            confirm_delete:"{{__("Do you want to delete?")}}",
            confirm_recovery:"{{__("Do you want to restore?")}}",
            confirm:"{{__("Confirm")}}",
            cancel:"{{__("Cancel")}}",
            custom_range: "{{ __("Custom Range") }}",
            apply: "{{ __("Apply") }}"
        };
        var daterangepickerLocale = {
            "applyLabel": "{{__('Apply')}}",
            "cancelLabel": "{{__('Cancel')}}",
            "fromLabel": "{{__('From')}}",
            "toLabel": "{{__('To')}}",
            "customRangeLabel": "{{__('Custom')}}",
            "weekLabel": "{{__('W')}}",
            "first_day_of_week": {{ setting_item("site_first_day_of_the_weekin_calendar","1") }},
            "daysOfWeek": [
                "{{__('Su')}}",
                "{{__('Mo')}}",
                "{{__('Tu')}}",
                "{{__('We')}}",
                "{{__('Th')}}",
                "{{__('Fr')}}",
                "{{__('Sa')}}"
            ],
            "monthNames": [
                "{{__('January')}}",
                "{{__('February')}}",
                "{{__('March')}}",
                "{{__('April')}}",
                "{{__('May')}}",
                "{{__('June')}}",
                "{{__('July')}}",
                "{{__('August')}}",
                "{{__('September')}}",
                "{{__('October')}}",
                "{{__('November')}}",
                "{{__('December')}}"
            ],
        };

        var image_editer = {
            language: '{{ app()->getLocale() }}',
            translations: {
                {{ app()->getLocale() }}: {
                    'header.image_editor_title': '{{ __('Image Editor') }}',
                    'header.toggle_fullscreen': '{{ __('Toggle fullscreen') }}',
                    'header.close': '{{ __('Close') }}',
                    'header.close_modal': '{{ __('Close window') }}',
                    'toolbar.download': '{{ __('Save Change') }}',
                    'toolbar.save': '{{ __('Save') }}',
                    'toolbar.apply': '{{ __('Apply') }}',
                    'toolbar.saveAsNewImage': '{{ __('Save As New Image') }}',
                    'toolbar.cancel': '{{ __('Cancel') }}',
                    'toolbar.go_back': '{{ __('Go Back') }}',
                    'toolbar.adjust': '{{ __('Adjust') }}',
                    'toolbar.effects': '{{ __('Effects') }}',
                    'toolbar.filters': '{{ __('Filters') }}',
                    'toolbar.orientation': '{{ __('Orientation') }}',
                    'toolbar.crop': '{{ __('Crop') }}',
                    'toolbar.resize': '{{ __('Resize') }}',
                    'toolbar.watermark': '{{ __('Watermark') }}',
                    'toolbar.focus_point': '{{ __('Focus point') }}',
                    'toolbar.shapes': '{{ __('Shapes') }}',
                    'toolbar.image': '{{ __('Image') }}',
                    'toolbar.text': '{{ __('Text') }}',
                    'adjust.brightness': '{{ __('Brightness') }}',
                    'adjust.contrast': '{{ __('Contrast') }}',
                    'adjust.exposure': '{{ __('Exposure') }}',
                    'adjust.saturation': '{{ __('Saturation') }}',
                    'orientation.rotate_l': '{{ __('Rotate Left') }}',
                    'orientation.rotate_r': '{{ __('Rotate Right') }}',
                    'orientation.flip_h': '{{ __('Flip Horizontally') }}',
                    'orientation.flip_v': '{{ __('Flip Vertically') }}',
                    'pre_resize.title': '{{ __('Would you like to reduce resolution before editing the image?') }}',
                    'pre_resize.keep_original_resolution': '{{ __('Keep original resolution') }}',
                    'pre_resize.resize_n_continue': '{{ __('Resize & Continue') }}',
                    'footer.reset': '{{ __('Reset') }}',
                    'footer.undo': '{{ __('Undo') }}',
                    'footer.redo': '{{ __('Redo') }}',
                    'spinner.label': '{{ __('Processing...') }}',
                    'warning.too_big_resolution': '{{ __('The resolution of the image is too big for the web. It can cause problems with Image Editor performance.') }}',
                    'common.x': '{{ __('x') }}',
                    'common.y': '{{ __('y') }}',
                    'common.width': '{{ __('width') }}',
                    'common.height': '{{ __('height') }}',
                    'common.custom': '{{ __('custom') }}',
                    'common.original': '{{ __('original') }}',
                    'common.square': '{{ __('square') }}',
                    'common.opacity': '{{ __('Opacity') }}',
                    'common.apply_watermark': '{{ __('Apply watermark') }}',
                    'common.url': '{{ __('URL') }}',
                    'common.upload': '{{ __('Upload') }}',
                    'common.gallery': '{{ __('Gallery') }}',
                    'common.text': '{{ __('Text') }}',
                }
            }
        };
    </script>
    <script src="{{ asset('libs/tinymce/js/tinymce/tinymce.min.js') }}" ></script>
    @stack('css')

</head>
<body class="{{($enable_multi_lang ?? '') ? 'enable_multi_lang' : '' }} @if(setting_item('site_enable_multi_lang')) site_enable_multi_lang @endif bg-gray-50 font-sans">

<!-- Layout Metronic avec Tailwind CSS -->
<div id="app" class="min-h-screen">

    <!-- Header Metronic -->
    <header class="main-header">
        @include('Layout::admin.parts.header')
    </header>

    <!-- Sidebar Metronic -->
    <aside class="main-sidebar">
        @include('Layout::admin.parts.sidebar')
    </aside>

    <!-- Contenu principal -->
    <main class="main-content">
        <!-- Breadcrumb -->
        <div class="mb-6">
            @include('Layout::admin.parts.bc')
        </div>

        <!-- Contenu de la page avec animation -->
        <div class="animate-fade-in">
            @yield('content')
        </div>

        <!-- Footer Metronic -->
        <footer class="main-footer mt-12">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-6 copy-right">
                        {{date('Y')}} &copy; {{__('Booking Core by')}}
                        <a href="{{__('https://www.bookingcore.co')}}" target="_blank" class="text-primary-500 hover:text-primary-600 font-medium transition-colors">
                            {{__('BookingCore Team')}}
                        </a>
                    </div>
                    <div class="col-md-6">
                        <div class="text-md-right footer-links d-none d-sm-block space-x-4">
                            <a href="{{__('https://www.bookingcore.co')}}" target="_blank" class="text-gray-600 hover:text-primary-500 transition-colors">{{__('About Us')}}</a>
                            <a href="{{__('https://m.me/bookingcore')}}" target="_blank" class="text-gray-600 hover:text-primary-500 transition-colors">{{__('Contact Us')}}</a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </main>

    <!-- Overlay pour sidebar mobile -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden sidebar-overlay" onclick="document.body.classList.remove('sidebar-open')"></div>
</div>

<!-- Styles d'animation supplémentaires -->
<style>
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Amélioration du sidebar mobile */
    .sidebar-open .sidebar-overlay {
        @apply block;
    }

    /* Transitions fluides */
    * {
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
    }
</style>

@include('Media::browser')

<!-- Scripts -->
{!! \App\Helpers\Assets::css(true) !!}
<script src="{{ asset('libs/pusher.min.js') }}"></script>
<script src="{{ asset('dist/admin/js/manifest.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/jquery-3.6.3.min.js?_ver='.config('app.asset_version')) }}" ></script>
<!-- Bootstrap JS remplacé par des fonctionnalités Tailwind/Metronic -->
<script>
// Fonctionnalités Metronic avec Tailwind
document.addEventListener('DOMContentLoaded', function() {
    // Gestion du sidebar mobile
    const sidebarToggle = document.querySelector('.btn-toggle-admin-menu');
    const sidebar = document.querySelector('.main-sidebar');
    const body = document.body;

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            body.classList.toggle('sidebar-open');
        });
    }

    // Gestion des dropdowns
    document.querySelectorAll('[data-toggle="dropdown"]').forEach(function(trigger) {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const dropdown = trigger.nextElementSibling;
            if (dropdown && dropdown.classList.contains('dropdown-menu')) {
                // Fermer tous les autres dropdowns
                document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                    if (menu !== dropdown) {
                        menu.classList.add('hidden');
                        menu.classList.remove('block');
                    }
                });

                // Toggle le dropdown actuel
                dropdown.classList.toggle('hidden');
                dropdown.classList.toggle('block');
            }
        });
    });

    // Fermer les dropdowns en cliquant à l'extérieur
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[data-toggle="dropdown"]')) {
            document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                menu.classList.add('hidden');
                menu.classList.remove('block');
            });
        }
    });

    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.metronic-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
<script src="{{ asset('dist/admin/js/vendor.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/filerobot-image-editor/filerobot-image-editor.min.js?_ver='.config('app.asset_version')) }}"></script>

<script src="{{ asset('dist/admin/js/app.js?_ver='.config('app.asset_version')) }}" ></script>
<script src="{{ asset('libs/vue/vue'.(!env('APP_DEBUG') ? '.min':'').'.js') }}"></script>

<script src="{{ asset('libs/select2/js/select2.min.js') }}" ></script>
<script src="{{ asset('libs/bootbox/bootbox.min.js') }}"></script>

<script src="{{url('libs/daterange/moment.min.js')}}"></script>
<script src="{{url('libs/daterange/daterangepicker.min.js?_ver='.config('app.asset_version'))}}"></script>

{!! \App\Helpers\Assets::js(true) !!}

@stack('js')

</body>
</html>
