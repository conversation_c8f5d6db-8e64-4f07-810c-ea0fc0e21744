<?php
$user = Auth::user();
[$notifications,$countUnread] = getNotify();

$languages = \Modules\Language\Models\Language::getActive();
$locale = App::getLocale();
$theme = \Modules\Theme\ThemeManager::currentProvider();
?>

<!-- Header Metronic moderne -->
<div class="flex items-center justify-between w-full h-16 px-6">

    <!-- Logo et menu toggle -->
    <div class="flex items-center space-x-4">
        <!-- Bouton menu mobile -->
        <button class="btn-toggle-admin-menu lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- Logo -->
        <div class="flex items-center">
            <h3 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                <a href="{{route('admin.index')}}" class="hover:opacity-80 transition-opacity">
                    DISCOVERY
                </a>
            </h3>
        </div>

        <!-- Lien vers le site -->
        <div class="hidden md:flex items-center">
            <a href="{{url('/')}}" target="_blank" class="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <span>{{__('View Site')}}</span>
            </a>
        </div>
    </div>

    <!-- Widgets de droite -->
    <div class="flex items-center space-x-4">

        <!-- Sélecteur de langue -->
        @if(!empty($languages) and is_enable_multi_lang())
        <div class="relative">
            <button class="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all" onclick="toggleDropdown('language-dropdown')">
                @foreach($languages as $language)
                    @if($locale == $language->locale)
                        @if($language->flag)
                            <span class="flag-icon flag-icon-{{$language->flag}} w-4 h-4"></span>
                        @endif
                        <span class="hidden sm:inline">{{$language->name}}</span>
                    @endif
                @endforeach
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>

            <div id="language-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                @foreach($languages as $language)
                    @php if($language->locale == $locale) continue; @endphp
                    <a href="{{route('language.set-admin-lang',['locale'=>$language->locale])}}" class="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                        @if($language->flag)
                            <span class="flag-icon flag-icon-{{$language->flag}} w-4 h-4"></span>
                        @endif
                        <span>{{$language->name}}</span>
                    </a>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Notifications -->
        <div class="relative">
            <button class="relative p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all" onclick="toggleDropdown('notifications-dropdown')">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                @if($countUnread > 0)
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {{$countUnread > 9 ? '9+' : $countUnread}}
                    </span>
                @endif
            </button>

            <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
                <!-- Header des notifications -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">{{__('Notifications')}} ({{$countUnread}})</h3>
                    <button class="markAllAsRead text-sm text-blue-600 hover:text-blue-700 font-medium">{{__('Mark all as read')}}</button>
                </div>

                <!-- Liste des notifications -->
                <div class="max-h-64 overflow-y-auto">
                    @if(count($notifications) > 0)
                        @foreach($notifications as $oneNotification)
                            @php
                                $active = $class = '';
                                $data = json_decode($oneNotification['data']);
                                $idNotification = @$data->id;
                                $usingData = @$data->notification;
                                $title = @$usingData->message;
                                $name = @$usingData->name;
                                $avatar = @$usingData->avatar;
                                $link = @$usingData->link;

                                if(empty($oneNotification->read_at)){
                                    $class = 'markAsRead';
                                    $active = 'bg-blue-50';
                                }
                            @endphp
                            <a href="{{$link}}" class="{{$class}} block p-4 hover:bg-gray-50 transition-colors {{$active}}" data-id="{{$idNotification}}">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        @if($avatar)
                                            <img class="w-8 h-8 rounded-full" src="{{$avatar}}" alt="{{$name}}">
                                        @else
                                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                                {{ucfirst($name[0] ?? 'N')}}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-900 line-clamp-2">{!! $title !!}</p>
                                        <p class="text-xs text-gray-500 mt-1">{{format_interval($oneNotification->created_at)}}</p>
                                    </div>
                                    @if(empty($oneNotification->read_at))
                                        <div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                                    @endif
                                </div>
                            </a>
                        @endforeach
                    @else
                        <div class="p-8 text-center text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                            </svg>
                            <p>{{__('No notifications')}}</p>
                        </div>
                    @endif
                </div>

                <!-- Footer -->
                @if(count($notifications) > 0)
                <div class="p-4 border-t border-gray-200 text-center">
                    <a href="{{route('core.admin.notification.loadNotify')}}" class="text-sm text-blue-600 hover:text-blue-700 font-medium">{{__('View All Notifications')}}</a>
                </div>
                @endif
            </div>
        </div>

        <!-- Menu utilisateur -->
        <div class="relative">
            <button class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-all" onclick="toggleDropdown('user-dropdown')">
                <div class="flex-shrink-0">
                    @if($avatar_url = $user->getAvatarUrl())
                        <img class="w-8 h-8 rounded-full object-cover" src="{{$user->getAvatarUrl()}}" alt="{{$user->getDisplayName()}}">
                    @else
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                            {{ucfirst($user->getDisplayName()[0])}}
                        </div>
                    @endif
                </div>
                <div class="hidden md:block text-left">
                    <div class="text-sm font-medium text-gray-900">{{$user->getDisplayName()}}</div>
                    <div class="text-xs text-gray-500">{{ucfirst($user->role->name ?? '')}}</div>
                </div>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>

            <div id="user-dropdown" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <!-- Info utilisateur -->
                <div class="px-4 py-3 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        @if($avatar_url = $user->getAvatarUrl())
                            <img class="w-10 h-10 rounded-full object-cover" src="{{$user->getAvatarUrl()}}" alt="{{$user->getDisplayName()}}">
                        @else
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                                {{ucfirst($user->getDisplayName()[0])}}
                            </div>
                        @endif
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{$user->getDisplayName()}}</div>
                            <div class="text-xs text-gray-500">{{ucfirst($user->role->name ?? '')}}</div>
                        </div>
                    </div>
                </div>

                <!-- Menu items -->
                <div class="py-2">
                    <a href="{{route('user.admin.detail',['id'=>$user->id])}}" class="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>{{__('Edit Profile')}}</span>
                    </a>

                    <a href="{{route('user.admin.password',['id'=>$user->id])}}" class="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m6 0V7a2 2 0 00-2-2H9a2 2 0 00-2 2v2m6 0H9"></path>
                        </svg>
                        <span>{{__('Change Password')}}</span>
                    </a>
                </div>

                <div class="border-t border-gray-200 py-2">
                    <div class="px-4 py-2">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">{{__("Vendor Dashboard")}}</div>
                    </div>
                    <a href="{{route('vendor.dashboard')}}" class="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span>{{__("Dashboard")}}</span>
                    </a>

                    <a href="{{url('/')}}" class="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <span>{{__("Homepage")}}</span>
                    </a>
                </div>

                <div class="border-t border-gray-200 py-2">
                    <button onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="flex items-center space-x-3 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        <span>{{__('Logout')}}</span>
                    </button>
                </div>
            </div>

            <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                {{ csrf_field() }}
            </form>
        </div>
    </div>
</div>

<!-- Script pour les dropdowns -->
<script>
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    const allDropdowns = document.querySelectorAll('[id$="-dropdown"]');

    // Fermer tous les autres dropdowns
    allDropdowns.forEach(d => {
        if (d.id !== dropdownId) {
            d.classList.add('hidden');
        }
    });

    // Toggle le dropdown actuel
    dropdown.classList.toggle('hidden');
}

// Fermer les dropdowns en cliquant à l'extérieur
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="-dropdown"]');
    const buttons = document.querySelectorAll('[onclick*="toggleDropdown"]');

    let clickedOnButton = false;
    buttons.forEach(button => {
        if (button.contains(event.target)) {
            clickedOnButton = true;
        }
    });

    if (!clickedOnButton) {
        dropdowns.forEach(dropdown => {
            if (!dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });
    }
});
</script>
